/*
 * DatabaseORA.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "DatabaseORA_MMS.h"
#include <sqlca.h>
#include <sql2oci.h>
#include <iostream>
using namespace std;

char tmpLog3[1024];
void log3(char *buf, int st, int err);
void get_timestring(char *fmt, long n, char *s);
char* trim(char* szOrg, int leng);

namespace KSKYB
{
int CDatabaseORA::setEnableThreads()
{
	m_bThread = true;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL ENABLE THREADS;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::setEnableThreads() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::initThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT ALLOCATE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::initThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::freeThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	pCtx = ctx;
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT FREE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::freeThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::connectToOracle(sql_context ctx, char* szUID, char* szDSN)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char szConnInf[20+1], szConnDsn[20+1];
	EXEC SQL END DECLARE SECTION;

	if ((szUID == NULL) || (szDSN == NULL)) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[szUID or szDSN is NULL]");
		log3(tmpLog3, 0, 0);
		return -1;
	}
	memset(szConnInf, 0x00, sizeof(szConnInf));
	memset(szConnDsn, 0x00, sizeof(szConnDsn));
	strcpy(szConnInf, szUID);
	strcpy(szConnDsn, szDSN);

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :szConnInf USING :szConnDsn;

	if (sqlca.sqlcode != 0) {
    	//20180829 ERROR[%d][%s] -> ERROR[%d][%.100s]
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[%d][%.100s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::connectToOracle() Success" << endl;
	return 1;
}

int CDatabaseORA::closeFromOracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL COMMIT WORK RELEASE;

	if (sqlca.sqlcode != 0) {
		//20180829 ERROR[%d][%s] -> ERROR[%d][%.100s]
		sprintf(tmpLog3, "CDatabaseORA::closeFromOracle() ERROR[%d][%.100s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::closeFromOracle() Success" << endl;
	return 1;
}
	


long long CDatabaseORA::getMsgData_V3(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char telco_name[24+1];
	long long mms_id;
	char cmms_id[30+1];
    char sender_key[40+1];
    char dst_addr[12+1];
    char template_cd[30+1];
	char button_name[50+1];
	char button_url[1000+1];
	char button[4000+1];
    char msg_body[2000+1];
	char res_method[8+1];
	//char timeout[2+1];
	char timeout[5+1];
	char title[50+1];
	
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE; 

	char s_mms_id[16+1];

	memset( ot_sqlmsg,   0x00, sizeof( ot_sqlmsg));
	memset( telco_name,  0x00, sizeof( telco_name));
	memset( cmms_id,	   0x00, sizeof( cmms_id));
	memset( sender_key,	 0x00, sizeof( sender_key));
	memset( dst_addr,    0x00, sizeof( dst_addr));
	memset( template_cd, 0x00, sizeof( template_cd));
	memset( button_name, 0x00, sizeof( button_name));
	memset( button_url,  0x00, sizeof( button_url));
	memset( button,      0x00, sizeof( button));
	memset( msg_body,    0x00, sizeof( msg_body));
	memset( res_method,  0x00, sizeof( res_method));
	memset( timeout,     0x00, sizeof( timeout));
	memset( s_mms_id,    0x00, sizeof( s_mms_id));
	memset( title,       0x00, sizeof( title));

	snprintf(telco_name, sizeof(telco_name), q_name);

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
		proc_get_msg_atk_v3( :telco_name, :cmms_id, :sender_key, :dst_addr\
			                 , :template_cd, :button_name, :button_url, :button\
			                 , :msg_body, :res_method, :timeout, :title, :ot_sqlcode, :ot_sqlmsg);
			                 
		END;
	END-EXEC;
	
	//sprintf(tmpLog3, "[TST0][%lld]", mms_id);
	//		log3(tmpLog3, 0, 0);

	
//	sprintf(s_mms_id, "%d", mms_id);
	//mms_id = atoll( cmms_id);
	//20180829 s_mms_id sprintf -> snprintf
	//sprintf( s_mms_id, "%s", cmms_id);
	//snprintf( s_mms_id, sizeof(s_mms_id), "%s", cmms_id);
	
	switch(ot_sqlcode) {
		case 0:
			mms_id = atoll( cmms_id);
			snprintf( s_mms_id, sizeof(s_mms_id), "%s", cmms_id);
			
			mapSend["mms_id"] = trimR(s_mms_id).c_str();
			mapSend["sender_key"] = trimR(sender_key).c_str();
			mapSend["template_code"] = trimR(template_cd).c_str();
			mapSend["dst_addr"] = trimR(dst_addr).c_str();
			mapSend["button_name"] = trimR(button_name).c_str();
			mapSend["button_url"] = trimR(button_url).c_str();
			mapSend["button"] = trimR(button).c_str();
			mapSend["msg_body"] = trimR(msg_body).c_str();
			mapSend["res_method"] = trimR(res_method).c_str();
			mapSend["timeout"] = trimR(timeout).c_str();
			mapSend["title"] = trimR(title).c_str();
			
			return mms_id;
		case -5:
		case -1405:
			break;
		default:
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg );
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData_V3() ERROR MMSID[%lld][%d][%s][%.100s]"\
			//                 , mms_id, ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
			sprintf(tmpLog3, "CDatabaseORA::getMsgData_V3() ERROR [%d][%.100s][%.100s]"\
			                 , ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			return -1;
	}

	return 0;
}


long long CDatabaseORA::getMsgData_V4(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char telco_name[24+1];
	long long mms_id;
	char cmms_id[30+1];
    char sender_key[40+1];
    char dst_addr[12+1];
    char template_cd[30+1];
	char button_name[50+1];
	char button_url[1000+1];
	char button[4000+1];
    char msg_body[2000+1];
	char res_method[8+1];
	//char timeout[2+1];
	char timeout[5+1];
	char title[50+1];
	char price[10+1];
	char cur_type[3+1]; // currency unit
	char message_type[2+1]; // alimtalk message type
	char kko_header[16+1]; // kko header
	char attachment[4000+1]; // button item
	char supplement[4000+1]; // direct connection
	
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	char s_mms_id[16+1];

	memset( ot_sqlmsg,   0x00, sizeof( ot_sqlmsg));
	memset( telco_name,  0x00, sizeof( telco_name));
	memset( cmms_id,	   0x00, sizeof( cmms_id));
	memset( sender_key,	 0x00, sizeof( sender_key));
	memset( dst_addr,    0x00, sizeof( dst_addr));
	memset( template_cd, 0x00, sizeof( template_cd));
	memset( button_name, 0x00, sizeof( button_name));
	memset( button_url,  0x00, sizeof( button_url));
	memset( button,      0x00, sizeof( button));
	memset( msg_body,    0x00, sizeof( msg_body));
	memset( res_method,  0x00, sizeof( res_method));
	memset( timeout,     0x00, sizeof( timeout));
	memset( s_mms_id,    0x00, sizeof( s_mms_id));
	memset( title,       0x00, sizeof( title));
	memset( price,       0x00, sizeof( price));
	memset( cur_type,       0x00, sizeof( cur_type));
	memset( message_type,       0x00, sizeof( message_type));
	memset( kko_header,       0x00, sizeof( kko_header));
	memset( attachment,       0x00, sizeof( attachment));
	memset( supplement,       0x00, sizeof( supplement));

	snprintf(telco_name, sizeof(telco_name), q_name);

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
		proc_get_msg_atk_v4( :telco_name, :cmms_id, :sender_key, :dst_addr\
			                 , :template_cd, :button_name, :button_url, :button\
			                 , :msg_body, :res_method, :timeout, :title, :price, :cur_type, :message_type, :kko_header, :attachment, :supplement, :ot_sqlcode, :ot_sqlmsg);
			                 
		END;
	END-EXEC;
	
	//sprintf(tmpLog3, "[TST0][%lld]", mms_id);
	//		log3(tmpLog3, 0, 0);

	
//	sprintf(s_mms_id, "%d", mms_id);
	//mms_id = atoll( cmms_id);
	//20180829 s_mms_id sprintf -> snprintf
	//sprintf( s_mms_id, "%s", cmms_id);
	//snprintf( s_mms_id, sizeof(s_mms_id), "%s", cmms_id);
	
	switch(ot_sqlcode) {
		case 0:
			mms_id = atoll( cmms_id);
			snprintf( s_mms_id, sizeof(s_mms_id), "%s", cmms_id);
			
			mapSend["mms_id"] = trimR(s_mms_id).c_str();
			mapSend["sender_key"] = trimR(sender_key).c_str();
			mapSend["template_code"] = trimR(template_cd).c_str();
			mapSend["dst_addr"] = trimR(dst_addr).c_str();
			mapSend["button_name"] = trimR(button_name).c_str();
			mapSend["button_url"] = trimR(button_url).c_str();
			mapSend["button"] = trimR(button).c_str();
			mapSend["msg_body"] = trimR(msg_body).c_str();
			mapSend["res_method"] = trimR(res_method).c_str();
			mapSend["timeout"] = trimR(timeout).c_str();
			mapSend["title"] = trimR(title).c_str();
			mapSend["price"] = trimR(price).c_str();
			mapSend["cur_type"] = trimR(cur_type).c_str();
			mapSend["message_type"] = trimR(message_type).c_str();
			mapSend["kko_header"] = trimR(kko_header).c_str();
			mapSend["attachment"] = trimR(attachment).c_str();
			mapSend["supplement"] = trimR(supplement).c_str();
			
			printf("SHS price[%s]",trimR(price).c_str());
			
			return mms_id;
		case -5:
		case -1405:
			break;
		default:
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg );
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData_V3() ERROR MMSID[%lld][%d][%s][%.100s]"\
			//                 , mms_id, ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
			sprintf(tmpLog3, "CDatabaseORA::getMsgData_V4() ERROR [%d][%.100s][%.100s]"\
			                 , ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			return -1;
	}

	return 0;
}

long long CDatabaseORA::getMsgData_V5(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char telco_name[24+1];
	long long mms_id;
	char cmms_id[30+1];
    char sender_key[40+1];
    char dst_addr[12+1];
    char template_cd[30+1];
	char button_name[50+1];
	char button_url[1000+1];
	char button[4000+1];
    char msg_body[2000+1];
	char res_method[8+1];
	//char timeout[2+1];
	char timeout[5+1];
	char title[50+1];
	char price[10+1];
	char cur_type[3+1]; // currency unit
	char message_type[2+1]; // alimtalk message type
	char kko_header[16+1]; // kko header
	char attachment[4000+1]; // button item
	char supplement[4000+1]; // direct connection
	char item_highlight[200+1];
	char item[2000+1];
	
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	char s_mms_id[16+1];

	memset( ot_sqlmsg,   0x00, sizeof( ot_sqlmsg));
	memset( telco_name,  0x00, sizeof( telco_name));
	memset( cmms_id,	   0x00, sizeof( cmms_id));
	memset( sender_key,	 0x00, sizeof( sender_key));
	memset( dst_addr,    0x00, sizeof( dst_addr));
	memset( template_cd, 0x00, sizeof( template_cd));
	memset( button_name, 0x00, sizeof( button_name));
	memset( button_url,  0x00, sizeof( button_url));
	memset( button,      0x00, sizeof( button));
	memset( msg_body,    0x00, sizeof( msg_body));
	memset( res_method,  0x00, sizeof( res_method));
	memset( timeout,     0x00, sizeof( timeout));
	memset( s_mms_id,    0x00, sizeof( s_mms_id));
	memset( title,       0x00, sizeof( title));
	memset( price,       0x00, sizeof( price));
	memset( cur_type,       0x00, sizeof( cur_type));
	memset( message_type,       0x00, sizeof( message_type));
	memset( kko_header,       0x00, sizeof( kko_header));
	memset( attachment,       0x00, sizeof( attachment));
	memset( supplement,       0x00, sizeof( supplement));
	memset( item_highlight,   0x00, sizeof( item_highlight));
	memset( item,   0x00, sizeof( item));

	snprintf(telco_name, sizeof(telco_name), q_name);

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
		proc_get_msg_atk_v5( :telco_name, :cmms_id, :sender_key, :dst_addr\
			                 , :template_cd, :button_name, :button_url, :button\
			                 , :msg_body, :res_method, :timeout, :title, :price
			                 , :cur_type, :message_type, :kko_header, :attachment, :supplement
			                 , :item_highlight, :item, :ot_sqlcode, :ot_sqlmsg);
			                 
		END;
	END-EXEC;
	
	//sprintf(tmpLog3, "[TST0][%lld]", mms_id);
	//		log3(tmpLog3, 0, 0);

	
//	sprintf(s_mms_id, "%d", mms_id);
	//mms_id = atoll( cmms_id);
	//20180829 s_mms_id sprintf -> snprintf
	//sprintf( s_mms_id, "%s", cmms_id);
	//snprintf( s_mms_id, sizeof(s_mms_id), "%s", cmms_id);
	
	switch(ot_sqlcode) {
		case 0:
			mms_id = atoll( cmms_id);
			snprintf( s_mms_id, sizeof(s_mms_id), "%s", cmms_id);
			
			mapSend["mms_id"] = trimR(s_mms_id).c_str();
			mapSend["sender_key"] = trimR(sender_key).c_str();
			mapSend["template_code"] = trimR(template_cd).c_str();
			mapSend["dst_addr"] = trimR(dst_addr).c_str();
			mapSend["button_name"] = trimR(button_name).c_str();
			mapSend["button_url"] = trimR(button_url).c_str();
			mapSend["button"] = trimR(button).c_str();
			mapSend["msg_body"] = trimR(msg_body).c_str();
			mapSend["res_method"] = trimR(res_method).c_str();
			mapSend["timeout"] = trimR(timeout).c_str();
			mapSend["title"] = trimR(title).c_str();
			mapSend["price"] = trimR(price).c_str();
			mapSend["cur_type"] = trimR(cur_type).c_str();
			mapSend["message_type"] = trimR(message_type).c_str();
			mapSend["kko_header"] = trimR(kko_header).c_str();
			mapSend["attachment"] = trimR(attachment).c_str();
			mapSend["supplement"] = trimR(supplement).c_str();
			mapSend["item_highlight"] = trimR(item_highlight).c_str();
			mapSend["item"] = trimR(item).c_str();
			
			printf("SHS price[%s]",trimR(price).c_str());
			
			return mms_id;
		case -5:
		case -1405:
			break;
		default:
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg );
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData_V3() ERROR MMSID[%lld][%d][%s][%.100s]"\
			//                 , mms_id, ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
			sprintf(tmpLog3, "CDatabaseORA::getMsgData_V5() ERROR [%d][%.100s][%.100s]"\
			                 , ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			return -1;
	}

	return 0;
}




int CDatabaseORA::setReportData(int telcoid, sql_context ctx, map<string, string> &mapReport)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	int ot_queue_sqlcode = -1;
	char ot_queue_sqlmsg[1024];

	char msg_id[50+1];
	long long mms_id;
	char cmms_id[32+1];
	char dlv_date[14+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	char res_code[4+1];
	char res_text[200+1];
	int telco_id;
	int res_type;
	char end_telco[5+1];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset( ot_sqlmsg,       0x00, sizeof( ot_sqlmsg));
	memset( ot_queue_sqlmsg, 0x00, sizeof( ot_queue_sqlmsg));
	memset( msg_id,          0x00, sizeof( msg_id));
	memset( dlv_date,        0x00, sizeof( dlv_date));
	memset( snd_numb,        0x00, sizeof( snd_numb));
	memset( rcv_numb,        0x00, sizeof( rcv_numb));
	memset( res_code,        0x00, sizeof( res_code));
	memset( res_text,        0x00, sizeof( res_text));
	memset( end_telco,       0x00, sizeof( end_telco));
	memset( cmms_id,	       0x00, sizeof(cmms_id));

	char sNum[24+1];
	char rNum[24+1];
	memset(sNum, 0x00, 24+1);
	memset(rNum, 0x00, 24+1);

	map<string, string>::iterator FindItr;
	
	FindItr = mapReport.find( "msg_id");
	mms_id = atoll( FindItr->second.c_str());
	//20180829 cmms_id msg_id sprintf -> snprintf 
	//sprintf( cmms_id,"%s", FindItr->second.c_str());
	//sprintf( msg_id, "%s", FindItr->second.c_str());
	snprintf( cmms_id, sizeof(cmms_id), "%s", FindItr->second.c_str());
	snprintf( msg_id, sizeof(msg_id), "%s", FindItr->second.c_str());	//msg_id
	//sprintf(rNum, "%s", (char*)string(*(itrData + 2)).c_str()); //rcv_phn_id

	FindItr = mapReport.find( "dlv_date");
	//20180829 dlv_date sprintf -> snprintf
	//sprintf( dlv_date, "%s", FindItr->second.c_str()); //reg_snd_dttm
	snprintf( dlv_date, sizeof(dlv_date), "%s", FindItr->second.c_str()); //reg_snd_dttm
	if( strcmp( dlv_date, "") == 0 || strcmp( dlv_date, "              ") == 0)
	{// If there is no time sent to the carrier, enter an arbitrary value
		strcpy(dlv_date,"19710101000000");
	}

	FindItr = mapReport.find( "res_code");
	//20180829 res_code sprintf -> snprintf
	//sprintf( res_code, "%s", FindItr->second.c_str()); //rslt_val
	snprintf( res_code, sizeof(res_code), "%s", FindItr->second.c_str()); //rslt_val

	FindItr = mapReport.find( "end_telco");
	//20180829 end_telco sprintf -> snprintf
	//sprintf( end_telco, "%s", FindItr->second.c_str());
	snprintf( end_telco, sizeof(end_telco), "%s", FindItr->second.c_str());

	FindItr = mapReport.find( "res_text");
	//sprintf(res_text, FindItr->second.c_str());
	//strncpy( res_text, FindItr->second.c_str(), sizeof( res_text)-1);
	snprintf( res_text, sizeof(res_text), "%s", FindItr->second.c_str());
	
	
	telco_id = telcoid;
	res_type = 0;

	//strcpy(snd_numb, sNum);
	FindItr = mapReport.find( "rcv_numb");
	//20180829 rcv_numb sprintf -> snprintf
	//sprintf( rcv_numb, FindItr->second.c_str());
	snprintf( rcv_numb, sizeof(rcv_numb), "%s",FindItr->second.c_str());
	
	//int retry_cnt = 0;

	/*				
	cerr<<"cmms_id:"<<cmms_id<<endl;
	cerr<<"msg_id:"<<msg_id<<endl;
	cerr<<"dlv_date:"<<dlv_date<<endl;
	cerr<<"snd_numb:"<<snd_numb<<endl;
	cerr<<"rcv_numb:"<<rcv_numb<<endl;
	cerr<<"res_text:"<<res_text<<endl;
	cerr<<"telco_id:"<<telco_id<<endl;
	cerr<<"res_type:"<<res_type<<endl;
	cerr<<"end_telco:"<<end_telco<<endl;
	*/

//retry:

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
			proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb\
				             , :res_code, :res_text, :telco_id, :res_type, :end_telco\
				             , :ot_sqlcode, :ot_sqlmsg, :ot_queue_sqlcode, :ot_queue_sqlmsg);
		END;
	END-EXEC;

	if( ot_sqlcode != 0 )
	{
		/*if (strstr(ot_sqlmsg,"ORA-00001") && retry_cnt < 2)
		{
			ot_sqlcode = -1;
			memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
			retry_cnt++;
			sprintf( tmpLog3, "CDatabaseORA::setReportData() proc_set_rpt_skb retry MMSID[%lld][%d][%.100s]"\
			                  , mms_id, ot_sqlcode, ot_sqlmsg );
			log3(tmpLog3, 0, 0);
			goto retry;
		}*/
		sprintf(tmpLog3, "CDatabaseORA::setReportData() ERROR MMSID[%lld][%d][%.100s]"\
		                 , mms_id, ot_sqlcode, ot_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	if( ot_queue_sqlcode != 0 )
	{
		/*if (strstr(ot_queue_sqlmsg,"ORA-00001") && retry_cnt < 2)
		{
			ot_sqlcode = -1;
			memset(ot_queue_sqlmsg, 0x00, sizeof ot_queue_sqlmsg);
			retry_cnt++;
			sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_mms_rpt_queue retry MMSID[%lld][%d][%.100s]", mms_id, ot_queue_sqlcode, ot_queue_sqlmsg );
			log3(tmpLog3, 0, 0);
			goto retry;
		}*/

		sprintf(tmpLog3, "CDatabaseORA::setReportData() queue ERROR MMSID[%lld][%d][%.100s]"\
		                 , mms_id, ot_queue_sqlcode, ot_queue_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	/*if (retry_cnt > 0)
	{
		sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_rpt_skb retry success");
		log3(tmpLog3, 0, 0);
	}*/
	return 0;
}

int CDatabaseORA::setPollingReportData(int telcoid, sql_context ctx, map<string, string> &mapReport)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int  ot_sqlcode = -1;
	char ot_sqlmsg[ 1024];
	int  ot_queue_sqlcode = -1;
	char ot_queue_sqlmsg[ 1024];

	int telco_id;
	int res_type;
	long long mms_id;
	char msg_id[    50+1];
	char cmms_id[   32+1];
	char dlv_date[  14+1];
	char snd_numb[  12+1];
	char rcv_numb[  12+1];
	char res_code[  4+1];
	char res_text[  200+1];
	char end_telco[ 5+1];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
  EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset( ot_sqlmsg,       0x00, sizeof( ot_sqlmsg));
	memset( ot_queue_sqlmsg, 0x00, sizeof( ot_queue_sqlmsg));
	memset( msg_id,          0x00, sizeof( msg_id));
	memset( dlv_date,        0x00, sizeof( dlv_date));
	memset( snd_numb,        0x00, sizeof( snd_numb));
	memset( rcv_numb,        0x00, sizeof( rcv_numb));
	memset( res_code,        0x00, sizeof( res_code));
	memset( res_text,        0x00, sizeof( res_text));
	memset( end_telco,       0x00, sizeof( end_telco));
	memset( cmms_id,	       0x00, sizeof( cmms_id));

	char sNum[ 24+1];
	char rNum[ 24+1];
	memset( sNum, 0x00, 24+1);
	memset( rNum, 0x00, 24+1);

	map<string, string>::iterator FindItr;
	
	FindItr = mapReport.find( "msg_id");
	mms_id = atoll( FindItr->second.c_str());
	//20180829 cmms_id msg_id sprintf -> snprintf
	//sprintf( cmms_id,"%s", FindItr->second.c_str());
	//sprintf( msg_id, "%s", FindItr->second.c_str());	//msg_id
	snprintf( cmms_id, sizeof(cmms_id), "%s", FindItr->second.c_str());
	snprintf( msg_id, sizeof(msg_id), "%s", FindItr->second.c_str());	//msg_id
	//sprintf(rNum, "%s", (char*)string(*(itrData + 2)).c_str()); //rcv_phn_id

	FindItr = mapReport.find( "dlv_date");
	//20180829 dlv_date sprintf -> snprintf
	//sprintf( dlv_date, "%s", FindItr->second.c_str()); //reg_snd_dttm
	snprintf( dlv_date, sizeof(dlv_date) , "%s", FindItr->second.c_str()); //reg_snd_dttm
	if( strcmp( dlv_date, "") == 0 || strcmp( dlv_date, "              ") == 0)
	{// If there is no time sent to the carrier, enter an arbitrary value
		strcpy( dlv_date, "19710101000000");
	}

	FindItr = mapReport.find( "res_code");
	//20180829 res_code sprintf -> snprintf
	//sprintf( res_code, "%s", FindItr->second.c_str()); //rslt_val
	snprintf( res_code, sizeof(res_code), "%s", FindItr->second.c_str()); //rslt_val

	FindItr = mapReport.find( "end_telco");
	//20180829 end_telco sprintf -> snprintf
	//sprintf( end_telco, "%s", FindItr->second.c_str());
	snprintf( end_telco, sizeof(end_telco), "%s", FindItr->second.c_str());

	FindItr = mapReport.find( "res_text");
	//sprintf(res_text, FindItr->second.c_str());
	strncpy( res_text, FindItr->second.c_str(), sizeof(res_text)-1);
	
	telco_id = telcoid;
	res_type = 0;

	//strcpy(snd_numb, sNum);
	FindItr = mapReport.find( "rcv_numb");
	//20180829 rcv_numb sprintf -> snprintf
	//sprintf( rcv_numb, FindItr->second.c_str());
	snprintf( rcv_numb, sizeof(rcv_numb), "%s",  FindItr->second.c_str());
	
	//int retry_cnt = 0;

	/*				
	cerr<<"cmms_id:"<<cmms_id<<endl;
	cerr<<"msg_id:"<<msg_id<<endl;
	cerr<<"dlv_date:"<<dlv_date<<endl;
	cerr<<"snd_numb:"<<snd_numb<<endl;
	cerr<<"rcv_numb:"<<rcv_numb<<endl;
	cerr<<"res_text:"<<res_text<<endl;
	cerr<<"telco_id:"<<telco_id<<endl;
	cerr<<"res_type:"<<res_type<<endl;
	cerr<<"end_telco:"<<end_telco<<endl;
	*/

//retry:

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
			proc_set_rpt_skb( :cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb\
				              , :res_code, :res_text, :telco_id, :res_type, :end_telco\
				              , :ot_sqlcode, :ot_sqlmsg, :ot_queue_sqlcode, :ot_queue_sqlmsg);
		END;
	END-EXEC;

	if( ot_sqlcode != 0 )
	{
		if( strstr( ot_sqlmsg, "ORA-00001"))
		{
			ot_sqlcode = -1;
			memset( ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
			sprintf( tmpLog3, "CDatabaseORA::setPollingReportData() proc_set_rpt_skb already exist MMSID[%lld][%d][%.100s]"\
			                  , mms_id, ot_sqlcode, ot_sqlmsg );
			log3(tmpLog3, 0, 0);
			return 0;
		}
		sprintf(tmpLog3, "CDatabaseORA::setPollingReportData() ERROR MMSID[%lld][%d][%.100s]"\
		                 , mms_id, ot_sqlcode, ot_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	if( ot_queue_sqlcode != 0 )
	{
		/*if (strstr(ot_queue_sqlmsg,"ORA-00001") && retry_cnt < 2)
		{
			ot_sqlcode = -1;
			memset(ot_queue_sqlmsg, 0x00, sizeof ot_queue_sqlmsg);
			retry_cnt++;
			sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_mms_rpt_queue retry MMSID[%lld][%d][%.100s]", mms_id, ot_queue_sqlcode, ot_queue_sqlmsg );
			log3(tmpLog3, 0, 0);
			goto retry;
		}*/
		sprintf(tmpLog3, "CDatabaseORA::setPollingReportData() queue ERROR MMSID[%lld][%d][%.100s]"
		                 , mms_id, ot_queue_sqlcode, ot_queue_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	return 0;
}

void CDatabaseORA::sql_error(sql_context ctx, int* result)
{
	*result = -1;
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	sprintf(tmpLog3, "[DEB] sql error. oracle message: [%s]", sqlca.sqlerrm.sqlerrmc);
	log3(tmpLog3, 0, 0);
	EXEC SQL ROLLBACK;
	//exit(1);
}

int CDatabaseORA::setPollingReport_batch(int telcoid, sql_context ctx, int succSize,
	                                       char _msg_id[][51], char _dlv_date[][15], 
	                                       char _res_code[][5], char _res_text[][201],
	                                       char _end_telco[][6])
{
#ifdef DEBUG
	sprintf(tmpLog3, "[DEB] start setPollingReport_batch() param: inpusSize[%d]", succSize);
	log3(tmpLog3, 0, 0);
#endif

	if(1000 < succSize) succSize = 1000;

	EXEC SQL BEGIN DECLARE SECTION;
	//////////////////////////////////////////////////
	// declare variables
	int result = 0;
	static int input_size;
	const static int MAX_POLLING_SIZE = 1000;

	struct sqlca sqlca;

  //---- report table
	char msg_id[MAX_POLLING_SIZE][50+1];
	//char cmms_id[MAX_POLLING_SIZE][32+1];
	char dlv_date[MAX_POLLING_SIZE][20+1];
	char res_code[MAX_POLLING_SIZE][10+1];
	char res_text[MAX_POLLING_SIZE][200+1];
	char end_telco[MAX_POLLING_SIZE][5+1];
	int  telco_id[MAX_POLLING_SIZE];
	int  res_type[MAX_POLLING_SIZE];
	long long mms_id[MAX_POLLING_SIZE];

	//---- send table
	char in_cid[MAX_POLLING_SIZE][40+1];
	char in_ptn_sn[MAX_POLLING_SIZE][32+1];
	char in_resv_data[MAX_POLLING_SIZE][200+1];
	char in_dstaddr[MAX_POLLING_SIZE][16+1];

	//---- logon and code tables
	char v_app_name[MAX_POLLING_SIZE][20+1];
	char v_q_name[MAX_POLLING_SIZE][20+1];
	char v_send_report_yn[MAX_POLLING_SIZE][1+1];
	int  send_report_yn[MAX_POLLING_SIZE];
	
	//----- data to enqueue 
	char in_cid2[12+1];
	char in_ptn_sn2[32+1];
	char in_resv_data2[200+1];
	char in_dstaddr2[12+1];
	char dlv_date2[20+1];
	char res_code2[8+1];
	char end_telco2[8+1];
	int  telco_id2 = 0;
	long long mms_id2 = 0;
	
	int  send_yn = 0;
	char app_name[20+1];
	char q_name[20+1];
	
	EXEC SQL END DECLARE SECTION;

	EXEC SQL CONTEXT USE :ctx;
		
	EXEC SQL WHENEVER NOT FOUND CONTINUE;
  	//EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */
  	EXEC SQL WHENEVER SQLERROR DO sql_error(ctx, &result);

	//////////////////////////////////////////////////
	// initialize variables
	input_size = succSize;
#ifdef DEBUG1
	sprintf(tmpLog3, "[DEB] check inpusSize[%d]", input_size);
	log3(tmpLog3, 0, 0);
#endif

	for(int i = 0; input_size > i; ++i)
	{
		memset(msg_id[i],    0x00, sizeof(msg_id[i]));
		//memset(cmms_id[i],   0x00, sizeof(cmms_id[i]));
		memset(dlv_date[i],  0x00, sizeof(dlv_date[i]));
		memset(res_code[i],  0x00, sizeof(res_code[i]));
		memset(res_text[i],  0x00, sizeof(res_text[i]));
		memset(end_telco[i], 0x00, sizeof(end_telco[i]));

		telco_id[i] = telcoid; /* 83: KKO, fixed */
		res_type[i] = 0;
	}
	
	//////////////////////////////////////////////////
	// preprocessing data
	for(int i = 0; input_size > i; ++i)
	{
		snprintf(msg_id[i], sizeof(msg_id[i]), "%s", _msg_id[i]);
		//snprintf(cmms_id[i], sizeof(cmms_id[i]), "%s",  _msg_id[i]);
		mms_id[i] = atoll(_msg_id[i]);

		snprintf(res_code[i], sizeof(res_code[i]), "%s",  _res_code[i]);
		strncpy(res_text[i], _res_text[i], sizeof(res_text[i])-1);
		
		snprintf(dlv_date[i], sizeof(dlv_date[i]), "%s", _dlv_date[i]);
		if(strcmp(dlv_date[i], "") == 0 ||
			 strcmp(dlv_date[i], "              ") == 0)
		{
			// Put random data if delivery time does not exist.
			strcpy(dlv_date[i], "19710101000000");
		}
		snprintf(end_telco[i], sizeof(end_telco[i]), "%s",  _end_telco[i]);

		sprintf(tmpLog3, "[INF] insert data: msg_id[%s] "\
		                 " res_code[%s] dlv_date[%s] res_text[%s] "\
		                 "end_telco[%s] telco_id[%d]",
		        msg_id[i], res_code[i], dlv_date[i],
		        res_text[i], end_telco[i], telco_id[i]);
		log3(tmpLog3, 0, 0);
	}

	//////////////////////////////////////////////////
	// insert report table
#ifdef DEBUG
	sprintf(tmpLog3, "[DEB] insert data into table");
	log3(tmpLog3, 0, 0);
#endif
	EXEC SQL FOR :input_size
	  INSERT INTO TBL_MMS_RPT
	    (MMS_ID, MSG_ID, MSG_SEQ, RES_DATE,
	     DLV_DATE, RES_CODE, RES_TEXT, 
	     TELCO_ID, RES_TYPE, END_TELCO, DD)
	  VALUES
	    (:mms_id, :msg_id, TBL_MMS_RPT_SEQ.NEXTVAL, SYSDATE,
	     TO_DATE(:dlv_date, 'YYYYMMDDHH24MISS'), :res_code, :res_text,
	     :telco_id, :res_type, :end_telco, TO_CHAR(SYSDATE, 'DD'));
	
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "CDatabaseORA::setPollingReport_batch() insert report table[%d][%d][%.100s]", result,sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);    
			
		return result;		
	}	     	
	//////////////////////////////////////////////////
	// select from send table
#ifdef DEBUG
	sprintf(tmpLog3, "[DEB] select data from table");
	log3(tmpLog3, 0, 0);
#endif
	for(int i = 0; input_size > i; ++i)
	{
    EXEC SQL 
    SELECT S.CID, S.PTN_SN, S.RESV_DATA, S.DSTADDR, 
           L.APPNAME, C.C_QNAME, L.SEND_REPORT_YN
    INTO :in_cid[i], :in_ptn_sn[i], :in_resv_data[i], :in_dstaddr[i],
    	   :v_app_name[i], :v_q_name[i], :v_send_report_yn[i]
    FROM TBL_LOGON L, TBL_CODE C, TBL_MMS_SEND S 
    WHERE L.PID = C.C_PTN_ID_NUM 
      AND L.JOB = C.C_IDX_INT 
      AND L.CID = S.CID
    	AND S.MMS_ID = :mms_id[i]
 	    AND rownum = 1;
           	   
    // No data exception
    
    if(sqlca.sqlcode != 0)
    {
    	sprintf(tmpLog3, "CDatabaseORA::setPollingReport_batch() select send table[%d][%d][%.100s]", result, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);    	  	
	
		return result;
	}

		if(0 == strcmp(v_send_report_yn[i], "Y"))
		{
			send_report_yn[i] = 1;
		}
		else
		{
			send_report_yn[i] = 0;
		}
		
#ifdef DEBUG
		sprintf(tmpLog3, "[DEB] data from select query.count[%d] "\ 
		                 "in_cid[%s] in_ptn_sn[%s] in_resv_data[%s] "\
		                 "in_dstaddr[%s] v_app_name[%s] v_q_name[%s] "\
		                 "v_send_report_yn[%s] send_report_yn[%d]",
		        i, 
		        in_cid[i], in_ptn_sn[i], in_resv_data[i],
		        in_dstaddr[i], v_app_name[i], v_q_name[i],
		        v_send_report_yn[i], send_report_yn[i]);
		log3(tmpLog3, 0, 0);
#endif
	}
	
  //////////////////////////////////////////////////
	// Insert rpt queue
  /* Embedded PLSQL call to the AQ dequeue procedure :*/
	for(int i = 0; input_size > i; ++i)
	{
		memset(in_cid2,       0x00, sizeof(in_cid2));
		memset(in_ptn_sn2,    0x00, sizeof(in_ptn_sn2));
		memset(in_resv_data2, 0x00, sizeof(in_resv_data2));
		memset(in_dstaddr2,   0x00, sizeof(in_dstaddr2));
		memset(dlv_date2,     0x00, sizeof(dlv_date2));
		memset(res_code2,     0x00, sizeof(res_code2));
		memset(end_telco2,    0x00, sizeof(end_telco2));
		memset(app_name,      0x00, sizeof(app_name));
		memset(q_name,        0x00, sizeof(q_name));
		
		telco_id2 = telco_id[i];
		mms_id2 = mms_id[i];
		send_yn = send_report_yn[i];

		snprintf(in_cid2,       sizeof(in_cid2),       "%s",  in_cid[i]);
		snprintf(in_ptn_sn2,    sizeof(in_ptn_sn2),    "%s",  in_ptn_sn[i]);
		snprintf(in_resv_data2, sizeof(in_resv_data2), "%s",  in_resv_data[i]);
		snprintf(in_dstaddr2,   sizeof(in_dstaddr2),   "%s",  in_dstaddr[i]);
		snprintf(dlv_date2,     sizeof(dlv_date2),     "%s",  dlv_date[i]);
		snprintf(res_code2,     sizeof(res_code2),     "%s",  res_code[i]);
		snprintf(end_telco2,    sizeof(end_telco2),    "%s",  end_telco[i]);
		snprintf(app_name,      sizeof(app_name),      "%s",  v_app_name[i]);
		snprintf(q_name,        sizeof(q_name),        "%s",  v_q_name[i]);

		/*
		strcpy(in_cid2,       in_cid[i]);
		strcpy(in_ptn_sn2,    in_ptn_sn[i]);
		strcpy(in_resv_data2, in_resv_data[i]);
		strcpy(in_dstaddr2,   in_dstaddr[i]);
		strcpy(dlv_date2,     dlv_date[i]);
		strcpy(res_code2,     res_code[i]);
		strcpy(end_telco2,    end_telco[i]);
		strcpy(app_name,      v_app_name[i]);
		strcpy(q_name,        v_q_name[i]);
		*/
		
		sprintf(tmpLog3, "[INF] insert value into AQ. "\
		                 "in_cid[%s] in_ptn_sn[%s] res_code[%s] "\
		                 "telco_id[%d] end_telco[%s], mmsid[%lld]"\
		                 " resv_data[%s] dlv_date[%s]",
		        in_cid2, in_ptn_sn2, res_code2, 
		        telco_id2, end_telco2, mms_id2, 
		        in_resv_data2, dlv_date2);
		log3(tmpLog3, 0, 0);

#ifdef DEBUG1
		sprintf(tmpLog3, "[DEB] enqueuing. mmsid[%s]", mms_id2);
		log3(tmpLog3, 0, 0);
#endif
	
		EXEC SQL EXECUTE
	 	DECLARE
	  enqueue_options      DBMS_AQ.ENQUEUE_OPTIONS_T;
	  message_properties   DBMS_AQ.MESSAGE_PROPERTIES_T;
	  //payload              NEOATK1.REPORT_MMS_MSG;
	  payload              NEOATK.REPORT_MMS_MSG;
		msgid                RAW(16);
		
		in_cid_q         varchar2(12)  := :in_cid2;
		in_ptn_sn_q      varchar2(32)  := :in_ptn_sn2;
		in_dstaddr_q     varchar2(12)  := :in_dstaddr2;
		in_resv_data_q   varchar2(200) := :in_resv_data2;
		telco_id_q       number(2)     := :telco_id2;
		res_code_q       varchar2(8)   := :res_code2;
		mms_id_q         number        := :mms_id2;
		end_telco_q      varchar2(8)   := :end_telco2;
		dlv_date_q       varchar2(20)  := :dlv_date2;
		
		app_name_q       varchar2(20)  := :app_name;
		q_name_q         varchar2(20)  := :q_name;
		send_report_yn_q  number       := :send_yn;
		
		BEGIN
			IF 1 = send_report_yn_q THEN
			  message_properties.expiration := 86400;
				message_properties.correlation := app_name_q;

			  payload := REPORT_MMS_MSG(in_cid_q, in_ptn_sn_q, in_dstaddr_q,
					                        in_resv_data_q, telco_id_q, res_code_q,
					                        mms_id_q, end_telco_q,dlv_date_q);
	
			  DBMS_AQ.ENQUEUE(queue_name         => q_name_q,
			                  enqueue_options    => enqueue_options,
			                  message_properties => message_properties,
			                  payload            => payload,
			                  msgid              => msgid);
			END IF;
		END;
		END-EXEC;
		
		if(sqlca.sqlcode != 0)
    	{
    		sprintf(tmpLog3, "CDatabaseORA::setPollingReport_batch() insert report queue[%d][%d][%.100s]", result, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);    	  	
	
			return result;
		}
		
	}

	/* Commit work */
	EXEC SQL COMMIT;
	

	return result;
}

int CDatabaseORA::setTransferLMS(sql_context ctx, long long _mms_id)
{
	EXEC SQL BEGIN DECLARE SECTION;

	char in_tran_pr[16+1];
	char in_cid[40+1];
	char in_ptn_sn[32+1];
	char in_callback[16+1];
	char in_dstaddr[16+1];
	char in_replace_flag[1+1];
	char in_replace_title[200+1];
	char in_replace_msg[2000+1];

	char tr_callback[15+1];
	char tr_dstaddr[15+1];
	char tr_replace_title[100+1];
	long long in_mms_id = 0;

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;	
	
	EXEC SQL WHENEVER NOT FOUND CONTINUE;
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	
	EXEC SQL CONTEXT USE :ctx;

	memset(in_tran_pr		, 0x00	, sizeof(in_tran_pr	    ));
	memset(in_cid			, 0x00	, sizeof(in_cid		    ));
	memset(in_ptn_sn		, 0x00	, sizeof(in_ptn_sn	    ));
	memset(in_callback		, 0x00	, sizeof(in_callback	));
	memset(in_dstaddr		, 0x00	, sizeof(in_dstaddr		));
	memset(in_replace_flag	, 0x00	, sizeof(in_replace_flag));
	memset(in_replace_title	, 0x00	, sizeof(in_replace_title));
	memset(in_replace_msg	, 0x00	, sizeof(in_replace_msg	));
	
	memset(tr_callback		, 0x00	, sizeof(tr_callback	));
	memset(tr_dstaddr		, 0x00	, sizeof(tr_dstaddr	));
	memset(tr_replace_title	, 0x00	, sizeof(tr_replace_title	));
	
	in_mms_id = _mms_id;

	EXEC SQL
	SELECT	CID, PTN_SN, CALLBACK, DSTADDR,REPLACE_FLAG, REPLACE_TITLE, REPLACE_MSG
		INTO :in_cid, :in_ptn_sn, :in_callback, :in_dstaddr, :in_replace_flag,
			:in_replace_title, :in_replace_msg
		FROM TBL_MMS_SEND
		WHERE MMS_ID = :in_mms_id
		AND	ROWNUM = 1;

	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "[%s()][ERR] TBL_MMS_SEND select for transfer [%d][%.100s]", 
					__func__, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);    	  	
	
		return -1;
	}

	if (in_replace_flag[0] == 'S') {
		EXEC SQL 
		   select SEQ_TRANSFER_ID_SMS.NEXTVAL
		   into :in_tran_pr
		   from dual;
		   	
		if(sqlca.sqlcode != 0)
		{
			sprintf(tmpLog3, "[%s()][ERR] select sequence[%d][%.100s]",  __func__,
								sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);    	  	
	
			return -1;
		}
	}
	else {
		EXEC SQL 
	        select SEQ_TRANSFER_ID_MMS.NEXTVAL
	        into :in_tran_pr
	        from dual;
		   	
		if(sqlca.sqlcode != 0)
		{
			sprintf(tmpLog3, "[%s()][ERR] select sequence[%d][%.100s]",  __func__,
								sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);    	  	
	
			return -1;
		}
	}
	
	return 0;	
}
	

}

/*
 * Output time string.
 */
void get_timestring(char *fmt, long n, char *s)
{
    struct tm *localt;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec);
}

char* trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

void log3(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", "DatabaseORA", 0, "");
	}
}
